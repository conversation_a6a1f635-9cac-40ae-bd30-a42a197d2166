import { useTranslations } from 'next-intl'
import type { GetCategoryProductsQuery } from '@ninebot/core/src/graphql/generated/graphql'
import { Breadcrumb as AntBreadcrumb } from 'antd'

import { Link } from '@/i18n/navigation'

type TCategoryItem = NonNullable<
  NonNullable<NonNullable<GetCategoryProductsQuery['categories']>['items']>[number]
>

interface BreadcrumbProps {
  category?: TCategoryItem
  currentPath?: string
}

// 递归查找匹配当前路径的分类
const findCategoryByPath = (
  categories: (TCategoryItem | null)[],
  currentPath: string,
): TCategoryItem | null => {
  for (const category of categories) {
    if (!category) continue

    const categoryPath = `/${category.url_path}${category.url_suffix}`
    if (currentPath === categoryPath) {
      return category
    }

    // 递归查找子分类
    if (category.children) {
      const found = findCategoryByPath(category.children, currentPath)
      if (found) return found
    }
  }
  return null
}

// 递归查找分类UID对应的分类信息（用于获取url_suffix）
const findCategoryByUid = (
  categories: (TCategoryItem | null)[],
  uid: string,
): TCategoryItem | null => {
  for (const category of categories) {
    if (!category) continue

    if (category.uid === uid) {
      return category
    }

    // 递归查找子分类
    if (category.children) {
      const found = findCategoryByUid(category.children, uid)
      if (found) return found
    }
  }
  return null
}

const Breadcrumb = ({ category, currentPath }: BreadcrumbProps) => {
  const getI18nString = useTranslations('Web')
  if (!category) return null

  // 根据当前路径找到对应的分类
  let targetCategory = category
  if (currentPath && category.children) {
    const foundCategory = findCategoryByPath([category, ...category.children], currentPath)
    if (foundCategory) {
      targetCategory = foundCategory
    }
  }

  // 构建面包屑路径，参考 Segway 的实现
  const breadcrumbParse = () => {
    const arr = [{ label: getI18nString('home'), href: '/' }]

    // 使用 GraphQL 返回的 breadcrumbs 字段，按 category_level 排序（值越小等级越高）
    if (targetCategory.breadcrumbs && targetCategory.breadcrumbs.length > 0) {
      const sortedBreadcrumbs = [...targetCategory.breadcrumbs]
        .filter((item): item is NonNullable<typeof item> => item !== null && item !== undefined) // 过滤掉 null/undefined
        .sort((a, b) => (a.category_level ?? 0) - (b.category_level ?? 0)) // 按 level 升序排序

      sortedBreadcrumbs.forEach((item, index) => {
        if (index === 0) {
          return
        }
        // 通过UID查找对应的分类信息以获取url_suffix
        const foundCategory = findCategoryByUid([category], item.category_uid ?? '')
        const urlSuffix = foundCategory?.url_suffix || targetCategory.url_suffix || ''

        arr.push({
          label: item.category_name ?? '',
          href: `/${item.category_url_path}${urlSuffix}`,
        })
      })
    }

    // 添加当前分类
    arr.push({
      label: targetCategory.name ?? '',
      href: currentPath || `/${targetCategory.url_path}${targetCategory.url_suffix}`,
    })

    return arr
  }

  const breadcrumbItems = breadcrumbParse()

  const items = breadcrumbItems.map((item, index) => {
    const isLast = index === breadcrumbItems.length - 1

    return {
      title: isLast ? (
        <span className="font-miSansMedium380 text-[#000000]">{item.label}</span>
      ) : (
        <Link href={item.href} className="font-miSansMedium380 text-[#444446] hover:!text-primary">
          {item.label}
        </Link>
      ),
    }
  })

  return (
    <div className="py-base-24">
      <AntBreadcrumb items={items} className="text-sm" />
    </div>
  )
}

export default Breadcrumb
